import { useEffect, useRef, useCallback, useState, memo } from "react";
import type { ReactNode } from "react";

/**
 * AutoScrollCarousel - A reusable carousel component with auto-scroll functionality
 *
 * Features:
 * - Auto-scroll functionality with configurable speed (30-50px/second recommended)
 * - Infinite loop with bidirectional movement (bounces between start and end)
 * - User interaction handling (pause on touch/mouse, resume after inactivity)
 * - Mobile-optimized (only activates auto-scroll on screens < 768px)
 * - Smooth transitions and seamless animation timing
 * - Maintains existing minimalist design aesthetic
 * - Integrates with theme system and scroll-snap functionality
 */

interface AutoScrollCarouselProps {
  /** The content to be displayed in the carousel */
  children: ReactNode;
  /** Additional CSS classes to apply to the carousel container */
  className?: string;
  /** Auto-scroll speed in pixels per second (default: 40) */
  scrollSpeed?: number;
  /** Duration to pause auto-scroll after user interaction in milliseconds (default: 3000) */
  pauseDuration?: number;
  /** Delay before auto-scroll begins when carousel first becomes active in milliseconds (default: 1500) */
  startDelay?: number;
  /** Whether auto-scroll should be active (default: false) */
  isActive?: boolean;
  /** Callback function called when user interacts with the carousel */
  onUserInteraction?: () => void;
}

export const AutoScrollCarousel = memo(function AutoScrollCarousel({
  children,
  className = "",
  scrollSpeed = 40,
  pauseDuration = 3000,
  startDelay = 1500,
  isActive = false,
  onUserInteraction,
}: AutoScrollCarouselProps) {
  const carouselRef = useRef<HTMLDivElement>(null);
  const autoScrollRef = useRef<number | null>(null);
  const userInteractionTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const startDelayTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [isUserInteracting, setIsUserInteracting] = useState(false);
  const [hasInitialDelayPassed, setHasInitialDelayPassed] = useState(false);
  const scrollDirectionRef = useRef<1 | -1>(1); // 1 for right, -1 for left
  const isActiveRef = useRef(isActive); // Track isActive changes
  const isMobileRef = useRef(window.innerWidth < 768); // Cache mobile check

  // Update mobile check on resize
  useEffect(() => {
    const handleResize = () => {
      isMobileRef.current = window.innerWidth < 768;
    };
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const startAutoScroll = useCallback(() => {
    if (!carouselRef.current || !isMobileRef.current) return;

    // CRITICAL: Always stop any existing animation before starting a new one
    if (autoScrollRef.current) {
      cancelAnimationFrame(autoScrollRef.current);
      autoScrollRef.current = null;
    }

    let lastTime = 0;
    const scroll = (currentTime: number) => {
      const container = carouselRef.current;
      if (!container) {
        if (autoScrollRef.current) {
          cancelAnimationFrame(autoScrollRef.current);
          autoScrollRef.current = null;
        }
        return;
      }

      if (lastTime === 0) lastTime = currentTime;
      const deltaTime = currentTime - lastTime;

      // Optimize frame rate to ~40fps for better battery life on mobile
      if (deltaTime >= 25) {
        const maxScroll = container.scrollWidth - container.clientWidth;
        const scrollAmount = (scrollSpeed * deltaTime) / 1000;
        const currentDirection = scrollDirectionRef.current;

        // Calculate new scroll position
        const newScrollLeft =
          container.scrollLeft + scrollAmount * currentDirection;

        // Check boundaries and reverse direction if needed
        if (newScrollLeft >= maxScroll) {
          scrollDirectionRef.current = -1;
          container.scrollLeft = maxScroll;
        } else if (newScrollLeft <= 0) {
          scrollDirectionRef.current = 1;
          container.scrollLeft = 0;
        } else {
          container.scrollLeft = newScrollLeft;
        }

        lastTime = currentTime;
      }

      autoScrollRef.current = requestAnimationFrame(scroll);
    };

    autoScrollRef.current = requestAnimationFrame(scroll);
  }, [scrollSpeed]);

  const stopAutoScroll = useCallback(() => {
    if (autoScrollRef.current) {
      cancelAnimationFrame(autoScrollRef.current);
      autoScrollRef.current = null;
    }
  }, []);

  // Handle user interaction
  const handleUserInteraction = useCallback(() => {
    setIsUserInteracting(true);
    stopAutoScroll();
    onUserInteraction?.();

    // Clear existing timeout
    if (userInteractionTimeoutRef.current) {
      clearTimeout(userInteractionTimeoutRef.current);
    }

    // Resume auto-scroll after specified pause duration
    userInteractionTimeoutRef.current = setTimeout(() => {
      setIsUserInteracting(false);
    }, pauseDuration);
  }, [stopAutoScroll, onUserInteraction, pauseDuration]);

  // Determine optimal scroll direction when resuming auto-scroll
  const setOptimalScrollDirection = useCallback(() => {
    if (!carouselRef.current) return;

    const container = carouselRef.current;
    const maxScroll = container.scrollWidth - container.clientWidth;
    const currentScroll = container.scrollLeft;
    const scrollPercentage = currentScroll / maxScroll;

    // Set direction based on current position to create smooth bidirectional movement
    if (scrollPercentage <= 0.5) {
      // In first half, continue going right
      scrollDirectionRef.current = 1;
    } else {
      // In second half, go left to create back-and-forth motion
      scrollDirectionRef.current = -1;
    }
  }, []);

  // Handle scroll boundaries for bidirectional movement
  const handleScroll = useCallback(() => {
    if (!carouselRef.current || !isUserInteracting) return;

    const container = carouselRef.current;
    const maxScroll = container.scrollWidth - container.clientWidth;
    const currentScroll = container.scrollLeft;

    // Update scroll direction based on current position when user stops interacting
    // This helps the auto-scroll continue in a logical direction
    if (currentScroll <= maxScroll * 0.1) {
      // Near the beginning, set direction to go right
      scrollDirectionRef.current = 1;
    } else if (currentScroll >= maxScroll * 0.9) {
      // Near the end, set direction to go left
      scrollDirectionRef.current = -1;
    }
    // For middle positions, keep the current direction
  }, [isUserInteracting]);

  // Update isActive ref to track changes
  useEffect(() => {
    isActiveRef.current = isActive;
  }, [isActive]);

  // Handle user interaction stopping auto-scroll
  useEffect(() => {
    if (isUserInteracting) {
      stopAutoScroll();
    }
  }, [isUserInteracting, stopAutoScroll]);

  // Handle user interaction resumption (after pause duration)
  useEffect(() => {
    if (
      !isUserInteracting &&
      isActive &&
      hasInitialDelayPassed &&
      window.innerWidth < 768
    ) {
      // Small delay to ensure state is stable
      const resumeTimeout = setTimeout(() => {
        if (!isUserInteracting && isActiveRef.current && carouselRef.current) {
          setOptimalScrollDirection();
          startAutoScroll();
        }
      }, 100);

      return () => clearTimeout(resumeTimeout);
    }
  }, [
    isUserInteracting,
    isActive,
    hasInitialDelayPassed,
    setOptimalScrollDirection,
    startAutoScroll,
  ]);

  // Simplified auto-scroll activation logic
  useEffect(() => {
    // Only proceed if on mobile and component is active
    if (!isActive || !isMobileRef.current || !carouselRef.current) {
      stopAutoScroll();
      if (startDelayTimeoutRef.current) {
        clearTimeout(startDelayTimeoutRef.current);
        startDelayTimeoutRef.current = null;
      }
      return;
    }

    // Don't start if user is currently interacting
    if (isUserInteracting) {
      return;
    }

    // If initial delay hasn't passed yet, set up the delay
    if (!hasInitialDelayPassed) {
      startDelayTimeoutRef.current = setTimeout(() => {
        if (
          isActiveRef.current &&
          !isUserInteracting &&
          carouselRef.current &&
          isMobileRef.current
        ) {
          setHasInitialDelayPassed(true);
          setOptimalScrollDirection();
          startAutoScroll();
        }
      }, startDelay);
    } else {
      // Initial delay has passed, start immediately
      setOptimalScrollDirection();
      startAutoScroll();
    }

    // Cleanup function
    return () => {
      stopAutoScroll();
      if (startDelayTimeoutRef.current) {
        clearTimeout(startDelayTimeoutRef.current);
        startDelayTimeoutRef.current = null;
      }
    };
  }, [
    isActive,
    isUserInteracting,
    hasInitialDelayPassed,
    startDelay,
    setOptimalScrollDirection,
    startAutoScroll,
    stopAutoScroll,
  ]);

  // Reset initial delay state when component becomes inactive
  useEffect(() => {
    if (!isActive) {
      setHasInitialDelayPassed(false);
    }
  }, [isActive]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopAutoScroll();
      if (userInteractionTimeoutRef.current) {
        clearTimeout(userInteractionTimeoutRef.current);
      }
      if (startDelayTimeoutRef.current) {
        clearTimeout(startDelayTimeoutRef.current);
        startDelayTimeoutRef.current = null;
      }
    };
  }, [stopAutoScroll]);

  return (
    <div
      ref={carouselRef}
      className={`overflow-x-auto pb-4 carousel-container ${className}`}
      onTouchStart={handleUserInteraction}
      onTouchMove={handleUserInteraction}
      onMouseDown={handleUserInteraction}
      onWheel={handleUserInteraction}
      onScroll={handleScroll}
      style={{ scrollBehavior: "auto" }}
    >
      {children}
    </div>
  );
});
