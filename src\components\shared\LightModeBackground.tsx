import { memo } from "react";

export const LightModeBackground = memo(function LightModeBackground() {
  return (
    <div className="fixed inset-0 w-full h-full overflow-hidden pointer-events-none theme-transition z-0">
      {/* Base light background - ensure full coverage with responsive viewport units */}
      <div className="absolute inset-0 w-full h-full bg-white mobile-viewport-height"></div>

      {/* Optimized animated gradient - hardware accelerated with reduced layers */}
      <div
        className="absolute inset-0 opacity-15"
        style={{
          background:
            "linear-gradient(45deg, #ffffff 0%, #ffffff 25%, #dcdcdc 25%, #dcdcdc 50%, #838485 50%, #838485 75%, #1c2120 75%, #1c2120 100%)",
          backgroundSize: "300% 300%",
          animation: "gradientShift 12s ease-in-out infinite",
          filter: "blur(2px)",
          willChange: "background-position",
          transform: "translate3d(0, 0, 0)", // Force hardware acceleration
        }}
      ></div>

      {/* Secondary gradient - optimized for performance */}
      <div
        className="absolute inset-0 opacity-8"
        style={{
          background:
            "linear-gradient(-45deg, #ffffff 0%, #ffffff 30%, #838485 30%, #838485 70%, #1c2120 70%, #1c2120 100%)",
          backgroundSize: "250% 250%",
          animation: "gradientShift 16s ease-in-out infinite reverse",
          filter: "blur(1px)",
          willChange: "background-position",
          transform: "translate3d(0, 0, 0)", // Force hardware acceleration
        }}
      ></div>

      {/* Subtle noise texture for depth - optimized */}
      <div
        className="absolute inset-0 opacity-2"
        style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 128 128' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.7' numOctaves='2' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E")`,
          filter: "blur(0.5px)",
          transform: "translate3d(0, 0, 0)", // Force hardware acceleration
        }}
      ></div>
    </div>
  );
});
