import { memo } from "react";
import logo from "@/assets/adeptos_logo.png";

interface WebAppHeaderProps {
  currentPhase: number;
  totalPhases: number;
  onBack: () => void;
  canGoBack: boolean;
}

export const WebAppHeader = memo(function WebAppHeader({
  currentPhase,
  totalPhases,
  onBack,
  canGoBack,
}: WebAppHeaderProps) {
  return (
    <header className="fixed top-0 left-0 right-0 z-40 backdrop-blur-lg theme-transition ">
      {/* Unified Layout for All Screen Sizes */}
      <div className="px-4 sm:px-6 lg:px-8">
        {/* Top Row: Back Button, Logo, and Phase Counter */}
        <div className="flex justify-between items-center py-3 sm:py-4 h-14 sm:h-16 md:h-20">
          {/* Left: Back Button + Logo */}
          <div className="flex items-center space-x-2 sm:space-x-3 md:space-x-4">
            {canGoBack ? (
              <button
                onClick={onBack}
                className="flex items-center justify-center w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 rounded-full bg-gray/10 dark:bg-light/10 hover:bg-gray/20 dark:hover:bg-light/20 theme-transition group"
                aria-label="Go back to previous phase"
              >
                <svg
                  className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 text-dark dark:text-light theme-transition group-hover:scale-110 transition-transform duration-200"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 19l-7-7 7-7"
                  />
                </svg>
              </button>
            ) : (
              <div className="w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12" />
            )}

            {/* Logo */}
            <div className="flex items-center">
              <img
                src={logo}
                alt="Adeptos AI Logo"
                className="w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 lg:w-16 lg:h-16 drop-shadow-sm hover:scale-105 transition-transform duration-200"
              />
              <span className="ml-2 sm:ml-3 text-sm sm:text-base md:text-lg lg:text-xl font-brand font-bold text-dark dark:text-white theme-transition">
                adeptos
                <span className="text-gray dark:text-gray">.ai</span>
              </span>
            </div>
          </div>

          {/* Right: Phase Counter */}
          <div className="text-xs sm:text-sm md:text-base font-body text-dark dark:text-white theme-transition">
            <span className="font-semibold">{currentPhase + 1}</span>
            <span className="text-gray dark:text-gray mx-1">/</span>
            <span className="text-gray dark:text-gray">{totalPhases}</span>
          </div>
        </div>

        {/* Bottom Row: Progress Bar */}
        <div className="pb-3 sm:pb-4">
          <div className="w-full h-1.5 sm:h-2 bg-gray/20 dark:bg-light/20 rounded-full overflow-hidden">
            <div
              className="h-full bg-dark dark:bg-light theme-transition transition-all duration-500 ease-out"
              style={{
                width: `${((currentPhase + 1) / totalPhases) * 100}%`,
              }}
            />
          </div>
        </div>
      </div>
    </header>
  );
});
