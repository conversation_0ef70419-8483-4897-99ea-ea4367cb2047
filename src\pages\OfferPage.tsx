import { useState } from "react";
import { useNavigate } from "react-router-dom";
import logo from "@/assets/adeptos_logo.png";

export function OfferPage() {
  const navigate = useNavigate();
  const [isPurchasing, setIsPurchasing] = useState(false);

  const handlePurchase = async () => {
    setIsPurchasing(true);
    await new Promise(resolve => setTimeout(resolve, 2000));
    setIsPurchasing(false);

    alert("Purchase functionality will be implemented when the payment API is ready.");
  };

  const benefits = [
    {
      title: "Detailed Implementation Roadmap",
      description: "A granular, step-by-step guide for your top 3 opportunities. No ambiguity."
    },
    {
      title: "Curated AI Tool Recommendations",
      description: "We'll tell you the exact software to use (and what to avoid) for your specific needs."
    },
    {
      title: "AI Agent Deployment Plan",
      description: "tailored to your specific needs."
    },
    {
      title: "EXCLUSIVE 1-on-1 AI STRATEGY SESSION",
      description: "A 30-minute video call with an Adeptos AI Expert to finalize your plan, answer your questions, and ensure you're set up for a massive win."
    },
    {
      title: "EXCLUSIVE access to our AI Adopters Community",
      description: "for discussions, free training, advice, tools, resources and more."
    }
  ];

  return (
    <div className="relative min-h-screen overflow-x-hidden">

      {/* Header */}
      <header className="fixed top-0 left-0 right-0 z-40 backdrop-blur-lg">
        <div className="px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-3 sm:py-4 h-14 sm:h-16 md:h-20">
            {/* Left: Back Button + Logo */}
            <div className="flex items-center space-x-2 sm:space-x-3 md:space-x-4">
              <button
                onClick={() => navigate(-1)}
                className="flex items-center justify-center w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 rounded-full bg-gray/10 dark:bg-light/10 hover:bg-gray/20 dark:hover:bg-light/20 theme-transition group"
                aria-label="Go back to previous page"
              >
                <svg
                  className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 text-dark dark:text-light theme-transition group-hover:scale-110 transition-transform duration-200"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 19l-7-7 7-7"
                  />
                </svg>
              </button>
              <img
                src={logo}
                alt="Adeptos AI Logo"
                className="w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 lg:w-16 lg:h-16 drop-shadow-sm hover:scale-105 transition-transform duration-200"
              />
              <span className="ml-2 sm:ml-3 text-sm sm:text-base md:text-lg lg:text-xl font-brand font-bold text-dark dark:text-white theme-transition">
                adeptos
                <span className="text-gray dark:text-gray">.ai</span>
              </span>
            </div>

            {/* Right: Offer Title */}
            <div className="text-xs sm:text-sm md:text-base font-body text-dark dark:text-white theme-transition">
              <span className="font-semibold">AI Implementation Plan</span>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="relative z-10 pt-20 sm:pt-24 md:pt-28 lg:pt-32 px-4 sm:px-6 lg:px-8 pb-8 min-h-screen">
        <div className="max-w-4xl mx-auto space-y-8">

          {/* Header Section */}
          <div className="text-center space-y-6">
            <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-title font-bold text-dark dark:text-white theme-transition leading-tight">
              AI Implementation Plan
            </h1>
            <p className="text-lg sm:text-xl md:text-2xl font-body text-dark dark:text-white theme-transition max-w-4xl mx-auto leading-relaxed">
              Go from Audit to Action. Get the Step-by-Step Plan to make AI your <span className="text-blue-600 dark:text-blue-400 font-bold">Unfair Advantage</span>.
            </p>
          </div>

          {/* Benefits Section */}
          <div className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 backdrop-blur-sm rounded-2xl p-6 sm:p-8 border border-blue-200/50 dark:border-blue-800/50 theme-transition shadow-lg">
            <h2 className="text-xl sm:text-2xl md:text-3xl font-title font-bold text-card-text theme-transition mb-8 text-center">
              What You'll Get:
            </h2>
            <div className="space-y-6">
              {benefits.map((benefit, index) => (
                <div key={index} className="bg-white/70 dark:bg-gray-800/50 rounded-xl p-4 sm:p-6 border border-blue-100 dark:border-blue-800/30 theme-transition hover:shadow-md transition-shadow duration-300">
                  <div className="flex items-start space-x-4">
                    {/* Checkmark */}
                    <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mt-1 shadow-lg">
                      <svg
                        className="w-5 h-5 text-white"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={3}
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg sm:text-xl font-title font-bold text-card-text theme-transition mb-2">
                        {benefit.title}
                      </h3>
                      <p className="text-base font-body text-card-label theme-transition leading-relaxed">
                        {benefit.description}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Urgency Section */}
          <div className="bg-gradient-to-r from-red-50 to-orange-50 dark:from-red-900/20 dark:to-orange-900/20 border-2 border-red-200 dark:border-red-800 rounded-2xl p-6 sm:p-8 theme-transition shadow-lg animate-pulse">
            <div className="text-center space-y-4">
              <div className="inline-flex items-center space-x-2 text-red-600 dark:text-red-400 mb-4">
                <span className="text-lg sm:text-xl font-title font-bold">⚠️ WARNING</span>
              </div>
              <p className="text-lg sm:text-xl font-body text-red-700 dark:text-red-300 font-bold">
                Our AI Experts only have <span className="text-2xl font-black text-red-800 dark:text-red-200">7 consultation slots</span> left for this month. Once they're gone, they're gone.
              </p>
            </div>
          </div>

          {/* Purchase Section */}
          <div className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-2xl p-6 sm:p-8 border border-green-200 dark:border-green-800 theme-transition shadow-lg">
            <div className="text-center space-y-6">
              {/* Price highlight */}
              <div className="space-y-2">
                <div className="text-gray-500 dark:text-gray-400 line-through text-lg font-body">
                  Regular Price: $199
                </div>
                <div className="text-3xl sm:text-4xl font-title font-bold text-green-600 dark:text-green-400">
                  Today Only: $29.99
                </div>
                <div className="text-red-600 dark:text-red-400 font-body font-semibold">
                  Save $169 (85% OFF)
                </div>
              </div>

              <button
                onClick={handlePurchase}
                disabled={isPurchasing}
                className="
                  px-8 sm:px-12 md:px-16 py-6 sm:py-7
                  text-xl sm:text-2xl font-body font-bold
                  bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700
                  text-white
                  rounded-full
                  transition-all duration-300
                  hover:scale-105
                  shadow-lg hover:shadow-2xl
                  backdrop-blur-sm
                  focus:outline-none
                  focus:ring-2
                  focus:ring-green-500/50
                  focus:ring-offset-2
                  focus:ring-offset-transparent
                  disabled:opacity-50
                  disabled:cursor-not-allowed
                  disabled:hover:scale-100
                  min-h-[72px] sm:min-h-[80px]
                  w-full max-w-4xl mx-auto
                  border-2 border-green-500
                  relative overflow-hidden
                  group
                "
              >
                <span className="relative z-10">
                  {isPurchasing
                    ? "Processing..."
                    : "🚀 Buy Action Plan for $29.99 and Book your Call with an AI Expert NOW!"
                  }
                </span>
                <div className="absolute inset-0 bg-gradient-to-r from-green-400 to-emerald-400 opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
              </button>

              <div className="space-y-3">
                <p className="text-sm text-gray-600 dark:text-gray-400 font-body max-w-2xl mx-auto">
                  Your consultation call will be scheduled immediately after purchase. Start implementing AI in your business within 24 hours.
                </p>
              </div>
            </div>
          </div>

        </div>
      </main>
    </div>
  );
}
