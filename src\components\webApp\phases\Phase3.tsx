import { Re<PERSON><PERSON><PERSON><PERSON>, type ReCaptchaRef } from "@/components/shared/ReCaptcha";

interface Phase3Props {
  recaptchaRef: React.RefObject<ReCaptchaRef | null>;
  submitError: string | null;
  isSubmitting: boolean;
  captchaToken: string | null;
  setCaptchaToken: (token: string | null) => void;
  handleFinalSubmit: () => void;
}

export function Phase3({ recaptchaRef, submitError, isSubmitting, captchaToken, setCaptchaToken, handleFinalSubmit }: Phase3Props) {
  return (
    <div className="w-full max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8 min-h-[calc(100dvh-8rem)] md:min-h-auto">
      <div className="space-y-8 sm:space-y-12">
        {/* Main Headline */}
        <div className="text-center space-y-4">
          <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-title font-bold text-dark dark:text-white theme-transition leading-tight">
            Almost Done! Complete Your Verification
          </h1>
        </div>
        <div className="text-center space-y-4 max-w-3xl mx-auto">
          <p className="text-lg sm:text-xl md:text-2xl font-body text-dark dark:text-white theme-transition leading-relaxed">
            Please complete the security verification below to receive your personalized AI business audit report.
          </p>
        </div>

        <div className="max-w-2xl mx-auto">
          <div className="space-y-6">
            <div className="flex justify-center">
              <ReCaptcha
                ref={recaptchaRef}
                onVerify={setCaptchaToken}
                className="mx-auto"
              />
            </div>

            {submitError && (
              <div className="text-center">
                <p className="text-red-500 text-sm font-medium">{submitError}</p>
              </div>
            )}

            <div className="text-center">
              <button
                type="button"
                onClick={handleFinalSubmit}
                disabled={isSubmitting || !captchaToken}
                className={`
                        px-8 sm:px-12 md:px-16 py-4 sm:py-5
                        text-lg sm:text-xl font-body font-bold
                        bg-dark dark:bg-light
                        text-white dark:text-dark
                        rounded-full
                        transition-all duration-300
                        hover:scale-105
                        shadow-lg hover:shadow-xl
                        theme-transition
                        backdrop-blur-sm
                        focus:outline-none
                        focus:ring-2
                        focus:ring-dark/50 dark:focus:ring-light/50
                        focus:ring-offset-2
                        focus:ring-offset-transparent
                        disabled:opacity-50
                        disabled:cursor-not-allowed
                        disabled:hover:scale-100
                        min-h-[56px] sm:min-h-[64px]
                        w-full max-w-md mx-auto
                        ${isSubmitting
                    ? "hover:bg-dark dark:hover:bg-light"
                    : "hover:bg-gray-800 dark:hover:bg-gray-200"
                  }
                      `}
                aria-label="Submit your AI business audit request"
              >
                {isSubmitting ? (
                  <div className="flex items-center justify-center space-x-2">
                    <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                    <span>Submitting...</span>
                  </div>
                ) : (
                  "Get My AI Business Audit"
                )}
              </button>
            </div>
          </div>
        </div>

        <div className="text-center max-w-3xl mx-auto">
          <p className="text-xs sm:text-sm font-body text-gray dark:text-gray theme-transition leading-relaxed">
            Your information is secure and will only be used to generate your personalized AI business report.
            By submitting this form, you agree to our privacy policy and terms of service.
          </p>
        </div>
      </div>
    </div>
  );
}