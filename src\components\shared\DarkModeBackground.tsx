import { memo } from "react";

export const DarkModeBackground = memo(function DarkModeBackground() {
  return (
    <div className="fixed inset-0 w-full h-full overflow-hidden pointer-events-none theme-transition z-0">
      {/* Dark base background - ensure full coverage with responsive viewport units */}
      <div className="absolute inset-0 w-full h-full bg-[#000000] mobile-viewport-height"></div>

      {/* Optimized animated gradient - hardware accelerated with reduced layers */}
      <div
        className="absolute inset-0 opacity-6"
        style={{
          background:
            "linear-gradient(45deg, #1c2120 0%, #1c2120 25%, #838485 25%, #838485 50%, #dcdcdc 50%, #dcdcdc 75%, #ffffff 75%, #ffffff 100%)",
          backgroundSize: "300% 300%",
          animation: "gradientShift 12s ease-in-out infinite",
          filter: "blur(2px)",
          willChange: "background-position",
          transform: "translate3d(0, 0, 0)", // Force hardware acceleration
        }}
      ></div>

      {/* Secondary gradient - optimized for performance */}
      <div
        className="absolute inset-0 opacity-3"
        style={{
          background:
            "linear-gradient(-45deg, #1c2120 0%, #1c2120 30%, #dcdcdc 30%, #dcdcdc 70%, #ffffff 70%, #ffffff 100%)",
          backgroundSize: "250% 250%",
          animation: "gradientShift 16s ease-in-out infinite reverse",
          filter: "blur(1px)",
          willChange: "background-position",
          transform: "translate3d(0, 0, 0)", // Force hardware acceleration
        }}
      ></div>

      {/* Subtle noise texture for depth - optimized */}
      <div
        className="absolute inset-0 opacity-1"
        style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 128 128' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.7' numOctaves='2' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E")`,
          filter: "blur(0.5px)",
          transform: "translate3d(0, 0, 0)", // Force hardware acceleration
        }}
      ></div>
    </div>
  );
});
