# Adeptos AI

Adeptos AI is a web application that provides a platform for AI-powered solutions. It offers a user-friendly interface for selecting and comparing AI solutions, as well as a comprehensive library of AI tools and resources.

## Features

- AI-Powered Solutions: Explore a wide range of AI solutions, including chatbots, virtual assistants, and AI-powered content creation tools.
- Intelligent Automation: Discover AI-powered automation solutions that can automate repetitive tasks and enhance productivity.
- Data-Driven Insights: Access AI-powered data analysis tools that can provide valuable insights into your data and help you make informed decisions.
- Scalable Innovation: Explore AI-powered innovation solutions that can help you develop and deploy AI-powered applications quickly and efficiently.

## Getting Started

To get started with Adeptos AI, follow these steps:

1. Clone the repository:

```bash
git clone https://github.com/SneatX/adeptosUi
```

2. Install dependencies:

```bash
npm install
```

3. Start the development server:

```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:port` to access the application.

## Technologies Used

- React.js: 19.1.0: A JavaScript library for building user interfaces.
- Tailwind CSS: 4.1.10: A utility-first CSS framework for rapidly building custom designs.
- Vite: 6.3.5: A fast, lightweight, and flexible build tool for modern web applications.
- TypeScript: A superset of JavaScript that adds static typing and other features to the language.
- ESLint: A tool for identifying and reporting on patterns found in ECMAScript/JavaScript code.
