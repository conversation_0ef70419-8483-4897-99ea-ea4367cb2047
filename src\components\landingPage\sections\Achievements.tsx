import { useEffect, useState, useRef } from "react";
import { AnimatedCounter } from "@/components/shared/AnimatedCounter";
import { RotatingContent } from "@/components/shared/RotatingContent";
import { FreeAuditButton } from "@/components/shared/FreeAuditButton";

export function Achievements() {
  const [isVisible, setIsVisible] = useState(false);
  const [animateCounters, setAnimateCounters] = useState(false);
  const sectionRef = useRef<HTMLDivElement>(null);

  const achievementMetrics = [
    {
      value: 90,
      suffix: "%+",
      label: "Process Efficiency",
      duration: 2000,
    },
    {
      value: 60,
      suffix: "%",
      label: "Cost Reduction in Labour",
      duration: 2200,
    },
    {
      value: 300,
      suffix: "%",
      label: "ROI average in 6-12 months",
      duration: 1800,
    },
    {
      value: 1000,
      suffix: "s",
      label: "Hours Saved a Year",
      duration: 1900,
    },
  ];

  const testimonials = [
    '"Adeptos.ai has revolutionized our approach to customer analytics. We\'ve seen a 40% increase in conversion rates since implementing their solution." - <PERSON>, CEO, Commercial Professional Refrigeration',
    '"The predictive maintenance solution from Adeptos.ai has reduced our downtime by 35% and saved us millions in operational costs." - <PERSON> Czechanski, Co-founder, 216 Maintenance',
    '"Implementing Adeptos.ai\'s NLP solution has transformed our customer service department. Response times are down 60% and satisfaction is up 45%." - Austin Huffman, CEO, Valvetronic Designs',
  ];

  // Mobile-optimized testimonials with shorter text
  const mobileTestimonials = testimonials;

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsVisible(true);
            setTimeout(() => setAnimateCounters(true), 500);
          } else {
            setIsVisible(false);
            setAnimateCounters(false);
          }
        });
      },
      {
        threshold: 0.3,
        rootMargin: "-10% 0px -10% 0px",
      }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    const currentRef = sectionRef.current;
    return () => {
      if (currentRef) {
        observer.unobserve(currentRef);
      }
    };
  }, []);

  return (
    <div
      ref={sectionRef}
      className="h-screen w-full overflow-x-hidden relative flex items-center justify-center theme-transition"
    >
      {/* Mobile Layout */}
      <div className="md:hidden w-full h-full flex flex-col justify-center items-center px-4 py-4">
        {/* Centered Content Group */}
        <div className="flex flex-col items-center justify-center space-y-4 max-w-xs w-full px-2">
          {/* Mobile Header */}
          <div
            className={`text-center transition-all duration-1000 ${isVisible
              ? "opacity-100 translate-y-0"
              : "opacity-0 translate-y-8"
              }`}
          >
            <h2 className="text-2xl sm:text-3xl font-brand font-bold text-dark dark:text-slate-100 theme-transition leading-tight">
              We don’t offer tools. We unlock potential
            </h2>
          </div>

          {/* Mobile Statistics - 2x2 Grid */}
          <div
            className={`transition-all duration-1000 delay-200 w-full ${isVisible
              ? "opacity-100 translate-y-0"
              : "opacity-0 translate-y-8"
              }`}
          >
            <div className="grid grid-cols-2 gap-2 w-full max-w-xs">
              {achievementMetrics.map((metric, index) => (
                <div
                  key={metric.label}
                  className={`text-center p-2 rounded-lg bg-(--card-bg) backdrop-blur-sm shadow-md transition-all duration-500 theme-transition ${isVisible
                    ? "opacity-100 translate-y-0"
                    : "opacity-0 translate-y-8"
                    }`}
                  style={{ transitionDelay: `${0.3 + index * 0.1}s` }}
                >
                  <div className="text-lg sm:text-xl font-bold text-(--card-text) font-brand mb-1 min-h-[1.25rem] flex items-center justify-center theme-transition">
                    <AnimatedCounter
                      target={animateCounters ? metric.value : 0}
                      duration={metric.duration}
                      suffix={metric.suffix}
                    />
                  </div>
                  <div className="text-xs text-(--card-label) font-body theme-transition">
                    {metric.label}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Mobile Testimonials */}
          <div
            className={`transition-all duration-1000 delay-400 ${isVisible
              ? "opacity-100 translate-y-0"
              : "opacity-0 translate-y-8"
              }`}
          >
            <div className="w-full px-2">
              <div className="mt-5 text-xs sm:text-sm text-dark dark:text-slate-200 font-body italic leading-relaxed text-center theme-transition">
                <RotatingContent items={mobileTestimonials} interval={4000} />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Desktop/Tablet Layout - Original Design */}
      <div className="hidden md:block w-full max-w-7xl mx-auto px-4 py-20">
        {/* Section Header */}
        <div
          className={`text-center mb-16 transition-all duration-1000 ${isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-8"
            }`}
        >
          <h2 className="text-4xl md:text-5xl font-brand font-bold text-dark dark:text-slate-100 mb-4 theme-transition">
            ROI Estimates across industies
          </h2>
        </div>

        {/* Enhanced Statistics Grid - Properly Centered */}
        <div className="flex justify-center mb-20">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 max-w-4xl w-full">
            {achievementMetrics.map((metric, index) => (
              <div
                key={metric.label}
                className={`text-center p-6 rounded-2xl bg-(--card-bg) backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-500 hover:scale-105 theme-transition ${isVisible
                  ? "opacity-100 translate-y-0"
                  : "opacity-0 translate-y-8"
                  }`}
                style={{ transitionDelay: `${0.2 + index * 0.1}s` }}
              >
                <div className="text-4xl md:text-5xl font-bold text-(--card-text) font-brand mb-2 min-h-[3.5rem] flex items-center justify-center theme-transition">
                  <AnimatedCounter
                    target={animateCounters ? metric.value : 0}
                    duration={metric.duration}
                    suffix={metric.suffix}
                  />
                </div>
                <div className="text-lg text-(--card-label) font-body theme-transition break-words">
                  {metric.label}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Testimonials Section - Properly Centered */}
        <div
          className={`mb-16 transition-all duration-1000 delay-1000 ${isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-8"
            }`}
        >
          <div className="flex justify-center">
            <div className="max-w-4xl w-full">
              <div className="text-lg md:text-xl text-dark dark:text-slate-200 font-body italic text-center leading-relaxed theme-transition break-words">
                <RotatingContent items={testimonials} interval={5000} />
              </div>
            </div>
          </div>
        </div>

        {/* Free AI Audit CTA Button */}
        <div
          className={`text-center mt-12 px-4 transition-all duration-1000 delay-1000 ${isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-8"
            }`}
        >
          <FreeAuditButton size="sm" className="w-full max-w-xs mx-auto" />
        </div>
      </div>
    </div>
  );
}
