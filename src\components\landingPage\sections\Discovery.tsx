import { useEffect, useState, useRef } from "react";
import { AutoScrollCarousel } from "@/components/shared/AutoScrollCarousel";
import { FreeAuditButton } from "@/components/shared/FreeAuditButton";
// import { AnimatedSectionTitle } from "@/components/AnimatedSectionTitle";

export function Discovery() {
  const [isVisible, setIsVisible] = useState(false);
  const [showTitle, setShowTitle] = useState(false);
  const [showContent, setShowContent] = useState(false);
  const sectionRef = useRef<HTMLDivElement>(null);
  const timeoutRefs = useRef<NodeJS.Timeout[]>([]);

  const services = [
    {
      icon: (
        <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12C4 7.59 7.59 4 12 4C16.41 4 20 7.59 20 12C20 16.41 16.41 20 12 20ZM13 7H11V13L16.25 16.15L17.25 14.45L13 11.75V7Z" />
        </svg>
      ),
      title: "Advanced Machine Learning",
      description:
        "Our proprietary algorithms adapt to your business needs, continuously improving performance over time.",
    },
    {
      icon: (
        <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12 2L3.09 8.26L12 14L20.91 8.26L12 2ZM21 16L12 22L3 16L12 10L21 16ZM12 8L7.91 5.09L12 2.18L16.09 5.09L12 8Z" />
        </svg>
      ),
      title: "ROI Impact Focused",
      description:
        "Our solutions are built around maximum impact for your business, providing innovative AI solutions to alleviate bottlenecks and maximize growth.",
    },
    {
      icon: (
        <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
          <path d="M20 2H4C2.9 2 2 2.9 2 4V22L6 18H20C21.1 18 22 17.1 22 16V4C22 2.9 21.1 2 20 2ZM20 16H5.17L4 17.17V4H20V16ZM7 9H17V11H7V9ZM7 12H15V14H7V12ZM7 6H17V8H7V6Z" />
        </svg>
      ),
      title: "NLP & RAG",
      description:
        "Communication with customers and team members through intelligent Chatbots trained on company data.",
    },
    {
      icon: (
        <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1L13.5 2.5L16.17 5.17C15.24 5.06 14.32 5 13.38 5C10.38 5 7.7 6.11 5.74 7.9L7.1 9.26C8.62 7.81 10.81 7 13.38 7C15.1 7 16.77 7.5 18.23 8.39L21 9ZM17.5 10C16.67 10 16 10.67 16 11.5S16.67 13 17.5 13S19 12.33 19 11.5S18.33 10 17.5 10ZM9.5 12C8.67 12 8 12.67 8 13.5S8.67 15 9.5 15S11 14.33 11 13.5S10.33 12 9.5 12ZM13.5 14C12.67 14 12 14.67 12 15.5S12.67 17 13.5 17S15 16.33 15 15.5S14.33 14 13.5 14Z" />
        </svg>
      ),
      title: "Custom AI Agents",
      description:
        "Our custom built AI Agents can automate your workflows to increase profitability, efficiency and help you scale without having to hire more humans.",
    },
    {
      icon: (
        <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12 1L3 5V11C3 16.55 6.84 21.74 12 23C17.16 21.74 21 16.55 21 11V5L12 1ZM12 7C13.1 7 14 7.9 14 9S13.1 11 12 11S10 10.1 10 9S10.9 7 12 7ZM18 11C18 15.52 15.52 19.45 12 20.88C8.48 19.45 6 15.52 6 11V6.3L12 3.19L18 6.3V11Z" />
        </svg>
      ),
      title: "Enterprise Level Security",
      description:
        "Protect your data with advanced AI-driven security features and anomaly detection.",
    },
    {
      icon: (
        <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
          <path d="M19 3H5C3.9 3 3 3.9 3 5V19C3 20.1 3.9 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3ZM19 19H5V5H19V19ZM17 12H15L13.5 7H10.5L9 12H7L9 17H15L17 12ZM12 10.5C12.83 10.5 13.5 9.83 13.5 9S12.83 7.5 12 7.5S10.5 8.17 10.5 9S11.17 10.5 12 10.5Z" />
        </svg>
      ),
      title: "Seamless Integration",
      description:
        "Easily connect with your existing tools and systems through our flexible API architecture.",
    },
  ];

  // Clear all timeouts
  const clearTimeouts = () => {
    timeoutRefs.current.forEach((timeout) => clearTimeout(timeout));
    timeoutRefs.current = [];
  };

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            // Clear any existing timeouts
            clearTimeouts();

            // Reset states and start sequence
            setIsVisible(true);
            setShowTitle(true);
            setShowContent(false);

            // After 2 seconds, fade out title
            const titleTimeout = setTimeout(() => {
              setShowTitle(false);
            }, 2000);

            // After 2.5 seconds total, fade in content
            const contentTimeout = setTimeout(() => {
              setShowContent(true);
            }, 2500);

            // Store timeouts for cleanup
            timeoutRefs.current = [titleTimeout, contentTimeout];
          } else {
            // Clear timeouts and reset states when leaving section
            clearTimeouts();
            setIsVisible(false);
            setShowTitle(false);
            setShowContent(false);
          }
        });
      },
      {
        threshold: 0.3,
        rootMargin: "-10% 0px -10% 0px",
      }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    const currentRef = sectionRef.current;
    return () => {
      clearTimeouts();
      if (currentRef) {
        observer.unobserve(currentRef);
      }
    };
  }, []);

  return (
    <div
      ref={sectionRef}
      className="h-screen w-full overflow-hidden relative theme-transition"
    >
      {/* Animated Section Title */}
      <div
        className={`absolute inset-0 flex items-center justify-center transition-all duration-1000 ${showTitle && isVisible ? "opacity-100" : "opacity-0"
          }`}
      >
        <div className="text-center">
          <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-8xl font-brand font-bold text-dark dark:text-slate-100 mb-4 theme-transition px-4 text-center">
            Discovery
          </h1>
          <p className="text-lg sm:text-xl md:text-2xl text-dark dark:text-light font-body theme-transition px-4 text-center">
            AI Solutions That Transform
          </p>
        </div>
      </div>

      {/* Services Grid */}
      <div
        className={`absolute inset-0 flex items-center justify-center transition-all duration-1000 ${showContent && isVisible
          ? "opacity-100 translate-y-0"
          : "opacity-0 translate-y-8"
          }`}
      >
        <div className="w-full max-w-6xl mx-auto px-4 md:px-8">
          {/* Mobile Layout - Horizontal Scrolling */}
          <div className="md:hidden">
            <AutoScrollCarousel
              isActive={showContent}
              scrollSpeed={40}
              pauseDuration={3000}
              startDelay={1500}
            >
              <div
                className="flex space-x-4 px-4"
                style={{ width: "max-content" }}
              >
                {services.map((service, index) => (
                  <div
                    key={`${service.title}-${index}`}
                    className="bg-white/80 dark:bg-gray-800/30 backdrop-blur-sm rounded-lg p-4 text-center transition-all duration-700 theme-transition flex-shrink-0"
                    style={{
                      transitionDelay: `${index * 0.15}s`,
                      width: "280px",
                      minWidth: "280px",
                    }}
                  >
                    <div className="text-dark dark:text-light mb-3 flex justify-center theme-transition">
                      <svg
                        className="w-8 h-8"
                        fill="currentColor"
                        viewBox="0 0 24 24"
                      >
                        {service.icon.props.children}
                      </svg>
                    </div>
                    <h3 className="text-lg font-semibold text-dark dark:text-light mb-2 font-title theme-transition break-words">
                      {service.title}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-slate-300 font-body leading-relaxed theme-transition break-words">
                      {service.description}
                    </p>
                  </div>
                ))}
              </div>
            </AutoScrollCarousel>
          </div>

          {/* Desktop Layout */}
          <div className="hidden md:block">
            <div className="grid grid-cols-2 lg:grid-cols-3 gap-8">
              {services.map((service, index) => (
                <div
                  key={service.title}
                  className="bg-white/10 dark:bg-gray-800/30 backdrop-blur-sm rounded-xl p-8 text-center hover:scale-105 transition-all duration-700 hover:shadow-xl theme-transition"
                  style={{ transitionDelay: `${index * 0.15}s` }}
                >
                  <div className="text-dark dark:text-light mb-6 flex justify-center theme-transition">
                    {service.icon}
                  </div>
                  <h3 className="text-xl font-semibold text-dark dark:text-slate-100 mb-4 font-title theme-transition break-words">
                    {service.title}
                  </h3>
                  <p className="text-gray-600 dark:text-slate-300 font-body leading-relaxed theme-transition break-words">
                    {service.description}
                  </p>
                </div>
              ))}
            </div>
          </div>

          {/* Free AI Audit CTA Button */}
          <div
            className={`text-center mt-12 px-4 transition-all duration-1000 delay-800 ${showContent
              ? "opacity-100 translate-y-0"
              : "opacity-0 translate-y-8"
              }`}
          >
            <FreeAuditButton size="md" className="w-full max-w-xs mx-auto" />
          </div>
        </div>
      </div>
    </div>
  );
}
