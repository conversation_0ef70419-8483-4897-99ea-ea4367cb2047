import { forwardRef, useImperativeHandle, useRef } from "react";
import ReCAPTC<PERSON> from "react-google-recaptcha";

interface ReCaptchaProps {
  onVerify?: (token: string | null) => void;
  className?: string;
  theme?: "light" | "dark";
  size?: "compact" | "normal";
}

export interface ReCaptchaRef {
  reset: () => void;
}

export const ReCaptcha = forwardRef<ReCaptchaRef, ReCaptchaProps>(
  ({ onVerify, className = "", theme = "light", size = "normal" }, ref) => {
    const recaptchaRef = useRef<ReCAPTCHA>(null);
    const siteKey = import.meta.env.VITE_RECAPTCHA_SITE_KEY;

    useImperativeHandle(ref, () => ({
      reset: () => {
        recaptchaRef.current?.reset();
      },
    }));

    const handleChange = (token: string | null) => {
      onVerify?.(token);
    };

    if (!siteKey) {
      return (
        <div className="text-center p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <p className="text-red-600 dark:text-red-400 text-sm font-medium">
            reCAPTCHA configuration error: Site key not found
          </p>
          <p className="text-red-500 dark:text-red-300 text-xs mt-1">
            Please check your environment configuration
          </p>
        </div>
      );
    }

    return (
      <div className={`${className}`}>
        <div className="flex flex-col items-center space-y-3">
          <ReCAPTCHA
            ref={recaptchaRef}
            sitekey={siteKey}
            onChange={handleChange}
            theme={theme}
            size={size}
            className="mx-auto"
          />
          <p className="text-xs text-gray-500 dark:text-gray-400 text-center max-w-sm">
            Please complete the reCAPTCHA verification to continue
          </p>
        </div>
      </div>
    );
  }
);

ReCaptcha.displayName = "ReCaptcha";
